"""
Data storage and persistence module for historical Bitcoin market data
"""
import pandas as pd
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
import csv
from typing import Dict, Any

def save_market_data(data: Dict[str, Any]) -> None:
    """
    Save market data snapshot to a CSV file.

    Columns: date, btc_price_usd, ahr999, fear_greed.
    Overwrites existing entry for the same date.
    """
    try:
        # Ensure the data directory exists
        os.makedirs(config.DATA_DIR, exist_ok=True)

        file_path = config.HISTORICAL_DATA_FILE
        fieldnames = ["date", "btc_price_usd", "ahr999", "fear_greed"]

        # Prepare row data
        row = {
            "date": data.get("date", datetime.utcnow().strftime("%Y-%m-%d")),
            "btc_price_usd": data.get("btc_price_usd"),
            "ahr999": data.get("ahr999"),
            "fear_greed": data.get("fear_greed"),
        }

        # Load existing rows if the file exists
        rows = []
        if os.path.exists(file_path):
            with open(file_path, mode="r", newline="", encoding="utf-8") as csvfile:
                reader = csv.DictReader(csvfile)
                for existing in reader:
                    if existing.get("date") == row["date"]:
                        # Overwrite the existing entry
                        existing = row
                    rows.append(existing)

        # Append new row if date not present
        if not any(r.get("date") == row["date"] for r in rows):
            rows.append(row)

        # Write all rows back to CSV
        with open(file_path, mode="w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)

        logger.info(f"Market data saved to {file_path}")
    except Exception as e:
        logger.error(f"Failed to save market data: {e}")


class DataStorage:
    """Handles storage and retrieval of historical market data"""
    
    def __init__(self):
        self.data_dir = config.DATA_DIR
        self.reports_dir = config.REPORTS_DIR
        self.historical_file = config.HISTORICAL_DATA_FILE
        
        # Create directories if they don't exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.reports_dir, exist_ok=True)
    
    def save_daily_data(self, market_data: Dict[str, Any]) -> bool:
        """
        Save daily market data to CSV file
        
        Args:
            market_data: Dictionary containing market data from DataFetcher
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Extract data for CSV storage
            row_data = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'timestamp': market_data['timestamp'],
                'btc_price': market_data['btc_price']['price'] if market_data['btc_price'] else None,
                'btc_source': market_data['btc_price']['source'] if market_data['btc_price'] else None,
                'fear_greed_value': market_data['fear_greed']['value'] if market_data['fear_greed'] else None,
                'fear_greed_classification': market_data['fear_greed']['value_classification'] if market_data['fear_greed'] else None,
                'ahr999_value': market_data['ahr999']['value'] if market_data['ahr999'] else None,
                'ahr999_note': market_data['ahr999']['note'] if market_data['ahr999'] else None
            }
            
            # Create DataFrame
            df_new = pd.DataFrame([row_data])

            # Get today's date
            today = datetime.now().strftime('%Y-%m-%d')

            # Check if file exists and load existing data
            if os.path.exists(self.historical_file):
                df_existing = pd.read_csv(self.historical_file)

                # Check if today's data already exists
                if today in df_existing['date'].values:
                    # Update existing record
                    df_existing.loc[df_existing['date'] == today] = row_data
                    df_combined = df_existing
                    logger.info(f"Updated existing data for {today}")
                else:
                    # Append new record
                    df_combined = pd.concat([df_existing, df_new], ignore_index=True)
                    logger.info(f"Added new data for {today}")
            else:
                # Create new file
                df_combined = df_new
                logger.info(f"Created new historical data file with data for {today}")
            
            # Sort by date and save
            df_combined = df_combined.sort_values('date')
            df_combined.to_csv(self.historical_file, index=False)
            
            # Also save as Excel for better readability
            excel_file = self.historical_file.replace('.csv', '.xlsx')
            df_combined.to_excel(excel_file, index=False)
            
            logger.info(f"Data saved successfully to {self.historical_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving daily data: {e}")
            return False
    
    def load_historical_data(self, days: Optional[int] = None) -> Optional[pd.DataFrame]:
        """
        Load historical data from CSV file
        
        Args:
            days: Number of recent days to load (None for all data)
            
        Returns:
            DataFrame with historical data or None if failed
        """
        try:
            if not os.path.exists(self.historical_file):
                logger.warning("Historical data file does not exist")
                return None
            
            df = pd.read_csv(self.historical_file)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date', ascending=False)
            
            if days:
                df = df.head(days)
            
            logger.info(f"Loaded {len(df)} records from historical data")
            return df
            
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return None
    
    def get_latest_data(self) -> Optional[Dict[str, Any]]:
        """
        Get the most recent data entry
        
        Returns:
            Dict with latest data or None if no data exists
        """
        try:
            df = self.load_historical_data(days=1)
            if df is None or df.empty:
                return None
            
            latest = df.iloc[0]
            return {
                'date': latest['date'],
                'btc_price': latest['btc_price'],
                'fear_greed_value': latest['fear_greed_value'],
                'fear_greed_classification': latest['fear_greed_classification'],
                'ahr999_value': latest['ahr999_value']
            }
            
        except Exception as e:
            logger.error(f"Error getting latest data: {e}")
            return None
    
    def save_analysis_report(self, report_data: Dict[str, Any]) -> bool:
        """
        Save analysis report to JSON file
        
        Args:
            report_data: Dictionary containing analysis results
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(self.reports_dir, f'analysis_report_{timestamp}.json')
            
            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            logger.info(f"Analysis report saved to {report_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving analysis report: {e}")
            return False
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics of stored data
        
        Returns:
            Dict containing data summary
        """
        try:
            df = self.load_historical_data()
            if df is None or df.empty:
                return {'status': 'No data available'}
            
            summary = {
                'total_records': len(df),
                'date_range': {
                    'start': df['date'].min().strftime('%Y-%m-%d'),
                    'end': df['date'].max().strftime('%Y-%m-%d')
                },
                'btc_price_stats': {
                    'current': df['btc_price'].iloc[0] if not df.empty else None,
                    'min': df['btc_price'].min(),
                    'max': df['btc_price'].max(),
                    'mean': df['btc_price'].mean()
                },
                'fear_greed_stats': {
                    'current': df['fear_greed_value'].iloc[0] if not df.empty else None,
                    'min': df['fear_greed_value'].min(),
                    'max': df['fear_greed_value'].max(),
                    'mean': df['fear_greed_value'].mean()
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating data summary: {e}")
            return {'status': 'Error generating summary'}
