"""
Reporting module for generating analysis summaries and console output
"""
import json
from typing import Dict, Any, Optional
from datetime import datetime
import logging
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Reporter:
    """Generates formatted reports and console output for analysis results"""
    
    def __init__(self):
        pass
    
    def generate_console_report(self, analysis: Dict[str, Any]) -> str:
        """
        Generate formatted console report from analysis results
        
        Args:
            analysis: Complete analysis results from SignalGenerator
            
        Returns:
            Formatted string report for console output
        """
        try:
            report_lines = []
            
            # Header
            report_lines.append("=" * 80)
            report_lines.append("🚀 BITCOIN MONITOR - DAILY ANALYSIS REPORT")
            report_lines.append("=" * 80)
            
            # Timestamp
            timestamp = analysis.get('timestamp', datetime.now().isoformat())
            report_lines.append(f"📅 Generated: {timestamp}")
            report_lines.append("")
            
            # Market Data Section
            market_data = analysis.get('market_data', {})
            report_lines.append("📊 CURRENT MARKET DATA")
            report_lines.append("-" * 40)
            
            if market_data.get('btc_price'):
                price = market_data['btc_price']['price']
                source = market_data['btc_price']['source']
                report_lines.append(f"💰 BTC Price: ${price:,.2f} (Source: {source})")
            
            if market_data.get('fear_greed'):
                fg_value = market_data['fear_greed']['value']
                fg_class = market_data['fear_greed']['value_classification']
                report_lines.append(f"😨 Fear & Greed Index: {fg_value}/100 ({fg_class})")
            
            if market_data.get('ahr999'):
                ahr999_value = market_data['ahr999']['value']
                report_lines.append(f"📈 AHR999 Index: {ahr999_value:.4f}")
            
            report_lines.append("")
            
            # Analysis Results Section
            analysis_results = analysis.get('analysis_results', {})
            report_lines.append("🔍 INDICATOR ANALYSIS")
            report_lines.append("-" * 40)
            
            # AHR999 Analysis
            if 'ahr999' in analysis_results:
                ahr999_analysis = analysis_results['ahr999']
                signal = ahr999_analysis.get('signal', 'UNKNOWN')
                interpretation = ahr999_analysis.get('interpretation', 'No interpretation available')
                report_lines.append(f"📊 AHR999 Signal: {signal}")
                report_lines.append(f"   └─ {interpretation}")
            
            # Fear & Greed Analysis
            if 'fear_greed' in analysis_results:
                fg_analysis = analysis_results['fear_greed']
                signal = fg_analysis.get('signal', 'UNKNOWN')
                interpretation = fg_analysis.get('interpretation', 'No interpretation available')
                report_lines.append(f"😰 Fear & Greed Signal: {signal}")
                report_lines.append(f"   └─ {interpretation}")
            
            # Trend Analysis
            if 'trend' in analysis_results:
                trend_analysis = analysis_results['trend']
                trend = trend_analysis.get('trend', 'UNKNOWN')
                strength = trend_analysis.get('strength', 'UNKNOWN')
                change_pct = trend_analysis.get('change_percent', 0)
                report_lines.append(f"📈 Price Trend: {trend} ({strength})")
                report_lines.append(f"   └─ Recent change: {change_pct:+.2f}%")
            
            report_lines.append("")
            
            # Recommendation Section
            recommendation = analysis.get('recommendation', {})
            if recommendation:
                report_lines.append("🎯 TRADING RECOMMENDATION")
                report_lines.append("-" * 40)
                
                main_rec = recommendation.get('recommendation', 'HOLD')
                confidence = recommendation.get('confidence', 'LOW')
                composite_score = recommendation.get('composite_score', 0)
                
                # Add emoji based on recommendation
                rec_emoji = {
                    'STRONG_BUY': '🟢🟢',
                    'BUY': '🟢',
                    'HOLD': '🟡',
                    'SELL': '🔴',
                    'STRONG_SELL': '🔴🔴'
                }.get(main_rec, '⚪')
                
                report_lines.append(f"{rec_emoji} Primary Recommendation: {main_rec}")
                report_lines.append(f"🎯 Confidence Level: {confidence}")
                report_lines.append(f"📊 Composite Score: {composite_score:.2f}")
            
            # Risk Assessment Section
            risk_assessment = analysis.get('risk_assessment', {})
            if risk_assessment:
                report_lines.append("")
                report_lines.append("⚠️  RISK ASSESSMENT")
                report_lines.append("-" * 40)
                
                risk_level = risk_assessment.get('risk_level', 'UNKNOWN')
                risk_score = risk_assessment.get('risk_score', 0)
                risk_factors = risk_assessment.get('risk_factors', [])
                
                # Risk level emoji
                risk_emoji = {
                    'LOW': '🟢',
                    'MEDIUM': '🟡',
                    'HIGH': '🔴'
                }.get(risk_level, '⚪')
                
                report_lines.append(f"{risk_emoji} Risk Level: {risk_level} (Score: {risk_score}/100)")
                
                if risk_factors:
                    report_lines.append("   Risk Factors:")
                    for factor in risk_factors:
                        report_lines.append(f"   • {factor}")
            
            # Actionable Signals Section
            signals = analysis.get('signals', {})
            if signals:
                report_lines.append("")
                report_lines.append("🎬 ACTIONABLE SIGNALS")
                report_lines.append("-" * 40)
                
                primary_action = signals.get('primary_action', 'HOLD')
                position_sizing = signals.get('position_sizing', 'NORMAL')
                specific_actions = signals.get('specific_actions', [])
                alerts = signals.get('alerts', [])
                
                report_lines.append(f"🎯 Primary Action: {primary_action}")
                report_lines.append(f"📏 Position Sizing: {position_sizing}")
                
                if specific_actions:
                    report_lines.append("   Specific Actions:")
                    for action in specific_actions:
                        report_lines.append(f"   • {action}")
                
                if alerts:
                    report_lines.append("   🚨 Alerts:")
                    for alert in alerts:
                        report_lines.append(f"   • {alert}")
            
            # Footer
            report_lines.append("")
            report_lines.append("=" * 80)
            report_lines.append("⚠️  DISCLAIMER: This analysis is for educational purposes only.")
            report_lines.append("   Always do your own research and consider your risk tolerance.")
            report_lines.append("=" * 80)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            logger.error(f"Error generating console report: {e}")
            return f"Error generating report: {e}"
    
    def generate_summary_report(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate summary report for notifications and alerts
        
        Args:
            analysis: Complete analysis results
            
        Returns:
            Dict containing summary information
        """
        try:
            market_data = analysis.get('market_data', {})
            recommendation = analysis.get('recommendation', {})
            risk_assessment = analysis.get('risk_assessment', {})
            signals = analysis.get('signals', {})
            
            summary = {
                'timestamp': analysis.get('timestamp'),
                'btc_price': market_data.get('btc_price', {}).get('price'),
                'recommendation': recommendation.get('recommendation', 'HOLD'),
                'confidence': recommendation.get('confidence', 'LOW'),
                'risk_level': risk_assessment.get('risk_level', 'UNKNOWN'),
                'primary_action': signals.get('primary_action', 'HOLD'),
                'alerts': signals.get('alerts', []),
                'key_metrics': {}
            }
            
            # Add key metrics
            if market_data.get('fear_greed'):
                summary['key_metrics']['fear_greed'] = {
                    'value': market_data['fear_greed']['value'],
                    'classification': market_data['fear_greed']['value_classification']
                }
            
            if market_data.get('ahr999'):
                summary['key_metrics']['ahr999'] = market_data['ahr999']['value']
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary report: {e}")
            return {'error': str(e)}
    
    def print_console_report(self, analysis: Dict[str, Any]) -> None:
        """
        Print formatted report to console
        
        Args:
            analysis: Complete analysis results
        """
        try:
            report = self.generate_console_report(analysis)
            print(report)
            
        except Exception as e:
            logger.error(f"Error printing console report: {e}")
            print(f"Error generating report: {e}")
    
    def save_report_to_file(self, analysis: Dict[str, Any], filename: Optional[str] = None) -> bool:
        """
        Save formatted report to text file
        
        Args:
            analysis: Complete analysis results
            filename: Optional custom filename
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"reports/bitcoin_analysis_report_{timestamp}.txt"
            
            report = self.generate_console_report(analysis)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"Report saved to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving report to file: {e}")
            return False
