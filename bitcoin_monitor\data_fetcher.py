"""
Data fetching module for Bitcoin price, AHR999 index, and Fear & Greed index
"""
import requests
import json
import time
from datetime import datetime
from typing import Dict, Optional, Any
import logging
from bs4 import BeautifulSoup
import config

def _request_json(url: str, timeout: int = 10):
    """Helper to perform GET request and return JSON with retries."""
    for attempt in range(3):
        try:
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
            if attempt < 2:
                time.sleep(2 ** attempt)  # exponential backoff
    logger.error(f"All attempts failed for {url}")
    return None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataFetcher:
    """Handles fetching of Bitcoin market data from various sources"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def fetch_btc_price(self) -> Optional[Dict[str, Any]]:
        """
        Fetch current BTC price from CoinAPI or fallback sources
        
        Returns:
            Dict containing BTC price data or None if failed
        """
        try:
            # Primary source: CoinAPI (if API key provided)
            if config.COINAPI_KEY:
                headers = {'X-CoinAPI-Key': config.COINAPI_KEY}
                response = self.session.get(config.COINAPI_BTC_URL, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                return {
                    'price': data['rate'],
                    'timestamp': datetime.now().isoformat(),
                    'source': 'coinapi'
                }
            
            # Fallback: CoinGecko (free API)
            else:
                data = _request_json(config.COINGECKO_PRICE_URL)
                if not data:
                    return None
                
                return {
                    'price': data['bitcoin']['usd'],
                    'timestamp': datetime.now().isoformat(),
                    'source': 'coingecko'
                }
                
        except Exception as e:
            logger.error(f"Error fetching BTC price: {e}")
            return None
    
    def fetch_fear_greed_index(self) -> Optional[Dict[str, Any]]:
        """
        Fetch Fear & Greed Index from Alternative.me API
        
        Returns:
            Dict containing Fear & Greed index data or None if failed
        """
        try:
            data = _request_json(config.FEAR_GREED_URL)
            if not data:
                return None
            
            if data['data']:
                latest = data['data'][0]
                return {
                    'value': int(latest['value']),
                    'value_classification': latest['value_classification'],
                    'timestamp': latest['timestamp'],
                    'source': 'alternative.me'
                }
            
        except Exception as e:
            logger.error(f"Error fetching Fear & Greed index: {e}")
            return None
    
    def fetch_ahr999_index(self) -> Optional[Dict[str, Any]]:
        """
        Fetch AHR999 index. This may require web scraping as it's not widely available via API.
        For now, we'll implement a calculation based on BTC price and 200-day moving average.
        
        Returns:
            Dict containing AHR999 index data or None if failed
        """
        try:
            # For demonstration, we'll calculate a simplified AHR999
            # In a real implementation, you'd need historical price data
            btc_data = self.fetch_btc_price()
            if not btc_data:
                return None
            
            current_price = btc_data['price']
            
            # Simplified calculation (in real implementation, use 200-day MA and 2-year MA)
            # AHR999 = (BTC Price / 200-day MA) / (2-year MA / 200-day MA)
            # For demo purposes, we'll use a mock calculation
            estimated_200day_ma = current_price * 0.85  # Simplified assumption
            estimated_2year_ma = current_price * 0.75   # Simplified assumption
            
            ahr999_value = (current_price / estimated_200day_ma) / (estimated_2year_ma / estimated_200day_ma)
            
            return {
                'value': round(ahr999_value, 4),
                'current_price': current_price,
                'timestamp': datetime.now().isoformat(),
                'source': 'calculated',
                'note': 'Simplified calculation for demonstration'
            }
            
        except Exception as e:
            logger.error(f"Error calculating AHR999 index: {e}")
            return None
    
    def fetch_market_data(self) -> Dict[str, Any]:
        """
        Fetch market data (BTC price, Fear & Greed, AHR999) and return a standardized dictionary.
        """
        logger.info("Fetching market data...")
        
        # Gather individual pieces
        btc_info = self.fetch_btc_price()
        ahr_info = self.fetch_ahr999_index()
        fg_info = self.fetch_fear_greed_index()

        # Build standardized dict
        data = {
            "date": datetime.utcnow().strftime('%Y-%m-%d'),
            "btc_price_usd": btc_info['price'] if btc_info else None,
            "ahr999": ahr_info['value'] if ahr_info else None,
            "fear_greed": fg_info['value'] if fg_info else None
        }
        
        # Add small delay between requests to be respectful to APIs
        time.sleep(1)
        
        logger.info("Data fetching completed")
        return data

def fetch_market_data():
    """
    Fetch market data and return a standardized dictionary.
    """
    fetcher = DataFetcher()
    date_str = datetime.utcnow().strftime('%Y-%m-%d')
    btc_info = fetcher.fetch_btc_price()
    ahr_info = fetcher.fetch_ahr999_index()
    fg_info = fetcher.fetch_fear_greed_index()

    return {
        "date": date_str,
        "btc_price_usd": btc_info['price'] if btc_info else None,
        "ahr999": ahr_info['value'] if ahr_info else None,
        "fear_greed": fg_info['value'] if fg_info else None
    }
