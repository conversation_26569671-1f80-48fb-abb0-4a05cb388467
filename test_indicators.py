"""
Test script for indicator calculations
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from bitcoin_monitor.indicators import IndicatorAnalyzer

def create_sample_data():
    """Create sample historical data for testing"""
    # Create dates for 3 years of data
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # Create realistic BTC price data with some volatility
    np.random.seed(42)  # For reproducible results
    prices = []
    base_price = 10000
    
    for i in range(len(dates)):
        # Add some trend and volatility
        trend = 0.0002 * i  # Small upward trend
        noise = np.random.normal(0, 0.02)  # Daily volatility
        price = base_price * (1 + trend) * (1 + noise)
        prices.append(max(price, 1000))  # Ensure minimum price
    
    # Reverse to have most recent dates first (as in the actual data)
    dates = dates[::-1]
    prices = prices[::-1]
    
    # Create DataFrame
    df = pd.DataFrame({
        'date': dates,
        'btc_price': prices,
        'fear_greed_value': np.random.randint(0, 100, len(dates))
    })
    
    return df

def test_indicators():
    """Test the indicator calculations"""
    # Create sample data
    df = create_sample_data()
    print(f"Created sample data with {len(df)} records")
    print(f"Date range: {df['date'].min()} to {df['date'].max()}")
    print(f"Price range: ${df['btc_price'].min():.2f} to ${df['btc_price'].max():.2f}")
    
    # Initialize analyzer
    analyzer = IndicatorAnalyzer()
    
    # Test AHR999 calculation
    print("\n=== Testing AHR999 Calculation ===")
    ahr999_value = analyzer.calculate_ahr999_index(df)
    print(f"AHR999 Index: {ahr999_value}")
    
    # Test price relative to moving averages
    print("\n=== Testing Price Relative to Moving Averages ===")
    price_ratios = analyzer.calculate_price_relative_to_ma(df)
    print(f"Price to 200-day MA ratio: {price_ratios['price_to_ma200']}")
    print(f"Price to 2-year MA ratio: {price_ratios['price_to_ma2year']}")
    
    # Test volatility calculation
    print("\n=== Testing Volatility Calculation ===")
    volatility_30 = analyzer.calculate_volatility_index(df, 30)
    volatility_90 = analyzer.calculate_volatility_index(df, 90)
    print(f"30-day volatility: {volatility_30}%")
    print(f"90-day volatility: {volatility_90}%")
    
    # Test Fear & Greed normalization
    print("\n=== Testing Fear & Greed Normalization ===")
    fear_greed_sample = 45
    normalized_fg = analyzer.normalize_fear_greed(fear_greed_sample)
    print(f"Raw Fear & Greed: {fear_greed_sample}")
    print(f"Normalized: {normalized_fg}")

if __name__ == "__main__":
    test_indicators()