#!/usr/bin/env python3
"""
BitcoinMonitor - Main application entry point
A Python project for monitoring Bitcoin market data and providing automated buy/sell recommendations
"""
import argparse
import sys
import os
import logging
from datetime import datetime
import schedule
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bitcoin_monitor.signal_generator import SignalGenerator
from bitcoin_monitor.reporter import Reporter
from bitcoin_monitor.notifications import NotificationManager
from bitcoin_monitor.data_storage import DataStorage, save_market_data
from bitcoin_monitor.data_fetcher import fetch_market_data
import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bitcoin_monitor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class BitcoinMonitor:
    """Main application class for Bitcoin monitoring and analysis"""
    
    def __init__(self):
        self.signal_generator = SignalGenerator()
        self.reporter = Reporter()
        self.notification_manager = NotificationManager()
        self.data_storage = DataStorage()
        
        logger.info("BitcoinMonitor initialized successfully")
    
    def run_analysis(self, send_notifications: bool = True, save_report: bool = True) -> dict:
        """
        Run complete Bitcoin market analysis
        
        Args:
            send_notifications: Whether to send notifications
            save_report: Whether to save report to file
            
        Returns:
            Dict containing analysis results
        """
        try:
            logger.info("Starting Bitcoin market analysis...")
            
            # Generate analysis
            analysis = self.signal_generator.generate_daily_analysis()
            
            if 'error' in analysis:
                logger.error(f"Analysis failed: {analysis['error']}")
                return analysis
            
            # Print console report
            self.reporter.print_console_report(analysis)
            
            # Save report to file
            if save_report:
                self.reporter.save_report_to_file(analysis)
            
            # Send notifications
            if send_notifications:
                notification_results = self.notification_manager.send_analysis_notifications(analysis)
                logger.info(f"Notification results: {notification_results}")
            
            logger.info("Analysis completed successfully")
            return analysis
            
        except Exception as e:
            logger.error(f"Error running analysis: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def run_scheduled_analysis(self):
        """Run analysis for scheduled execution"""
        logger.info("Running scheduled analysis...")
        self.run_analysis(send_notifications=True, save_report=True)
    
    def start_scheduler(self, interval_hours: int = 24):
        """
        Start scheduled analysis
        
        Args:
            interval_hours: Hours between analyses
        """
        logger.info(f"Starting scheduler with {interval_hours} hour intervals...")
        
        # Schedule daily analysis
        schedule.every(interval_hours).hours.do(self.run_scheduled_analysis)
        
        # Run initial analysis
        self.run_scheduled_analysis()
        
        # Keep running
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Scheduler stopped by user")
    
    def get_data_summary(self) -> dict:
        """Get summary of stored historical data"""
        return self.data_storage.get_data_summary()
    
    def send_test_notification(self) -> dict:
        """Send test notification to verify configuration"""
        test_message = f"Bitcoin Monitor test notification - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        return self.notification_manager.send_alert_notification(test_message, priority='NORMAL')


def main():
    """Main entry point with command line interface"""
    parser = argparse.ArgumentParser(description='Bitcoin Monitor - Automated Bitcoin market analysis')
    parser.add_argument('--mode', choices=['once', 'schedule', 'summary', 'test-notifications'], 
                       default='once', help='Execution mode')
    parser.add_argument('--interval', type=int, default=24, 
                       help='Hours between scheduled analyses (default: 24)')
    parser.add_argument('--no-notifications', action='store_true', 
                       help='Disable notifications')
    parser.add_argument('--no-save', action='store_true', 
                       help='Disable saving reports to file')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize monitor
    try:
        monitor = BitcoinMonitor()
    except Exception as e:
        logger.error(f"Failed to initialize BitcoinMonitor: {e}")
        sys.exit(1)
    
    # Execute based on mode
    try:
        if args.mode == 'once':
            # Run single analysis
            result = monitor.run_analysis(
                send_notifications=not args.no_notifications,
                save_report=not args.no_save
            )
            # Persist the fetched market data
            market_snapshot = fetch_market_data()
            save_market_data(market_snapshot)
            
            if 'error' in result:
                sys.exit(1)
                
        elif args.mode == 'schedule':
            # Run scheduled analysis
            monitor.start_scheduler(interval_hours=args.interval)
            
        elif args.mode == 'summary':
            # Show data summary
            summary = monitor.get_data_summary()
            print("\n" + "="*50)
            print("BITCOIN MONITOR DATA SUMMARY")
            print("="*50)
            for key, value in summary.items():
                print(f"{key}: {value}")
            print("="*50)
            
        elif args.mode == 'test-notifications':
            # Test notifications
            print("Testing notification configuration...")
            results = monitor.send_test_notification()
            print(f"Email: {'✓' if results['email'] else '✗'}")
            print(f"Telegram: {'✓' if results['telegram'] else '✗'}")
            
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
