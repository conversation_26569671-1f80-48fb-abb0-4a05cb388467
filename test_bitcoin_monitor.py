#!/usr/bin/env python3
"""
Simple test script for BitcoinMonitor functionality
"""
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bitcoin_monitor.data_fetcher import DataFetcher
from bitcoin_monitor.data_storage import DataStorage
from bitcoin_monitor.indicators import IndicatorAnal<PERSON>zer
from bitcoin_monitor.reporter import Reporter
from bitcoin_monitor.notifications import Notification<PERSON>ana<PERSON>


def test_data_fetcher():
    """Test data fetching functionality"""
    print("Testing Data Fetcher...")
    fetcher = DataFetcher()
    
    # Test BTC price fetching
    btc_data = fetcher.fetch_btc_price()
    if btc_data:
        print(f"✓ BTC Price: ${btc_data['price']:,.2f} (Source: {btc_data['source']})")
    else:
        print("✗ Failed to fetch BTC price")
    
    # Test Fear & Greed index
    fg_data = fetcher.fetch_fear_greed_index()
    if fg_data:
        print(f"✓ Fear & Greed: {fg_data['value']}/100 ({fg_data['value_classification']})")
    else:
        print("✗ Failed to fetch Fear & Greed index")
    
    # Test AHR999 calculation
    ahr999_data = fetcher.fetch_ahr999_index()
    if ahr999_data:
        print(f"✓ AHR999: {ahr999_data['value']:.4f}")
    else:
        print("✗ Failed to calculate AHR999")
    
    return btc_data, fg_data, ahr999_data


def test_data_storage():
    """Test data storage functionality"""
    print("\nTesting Data Storage...")
    storage = DataStorage()
    
    # Test data summary
    summary = storage.get_data_summary()
    print(f"✓ Data summary generated: {summary.get('status', 'OK')}")
    
    return storage


def test_indicators(btc_data, fg_data, ahr999_data):
    """Test indicator analysis"""
    print("\nTesting Indicator Analysis...")
    analyzer = IndicatorAnalyzer()
    
    # Test AHR999 analysis
    if ahr999_data:
        ahr999_analysis = analyzer.analyze_ahr999(ahr999_data['value'])
        print(f"✓ AHR999 Analysis: {ahr999_analysis.get('signal', 'ERROR')}")
    
    # Test Fear & Greed analysis
    if fg_data:
        fg_analysis = analyzer.analyze_fear_greed(fg_data['value'], fg_data['value_classification'])
        print(f"✓ Fear & Greed Analysis: {fg_analysis.get('signal', 'ERROR')}")
    
    return True


def test_reporter():
    """Test reporting functionality"""
    print("\nTesting Reporter...")
    reporter = Reporter()
    
    # Create mock analysis data
    mock_analysis = {
        'timestamp': '2024-01-15T10:30:00',
        'market_data': {
            'btc_price': {'price': 42350.75, 'source': 'test'},
            'fear_greed': {'value': 35, 'value_classification': 'Fear'},
            'ahr999': {'value': 0.8234}
        },
        'recommendation': {
            'recommendation': 'BUY',
            'confidence': 'MEDIUM',
            'composite_score': 0.75
        },
        'risk_assessment': {
            'risk_level': 'LOW',
            'risk_score': 15
        },
        'signals': {
            'primary_action': 'BUY',
            'specific_actions': ['Continue regular DCA strategy'],
            'alerts': []
        }
    }
    
    # Test console report generation
    report = reporter.generate_console_report(mock_analysis)
    if report and len(report) > 100:
        print("✓ Console report generated successfully")
    else:
        print("✗ Failed to generate console report")
    
    return True


def test_notifications():
    """Test notification configuration"""
    print("\nTesting Notification Configuration...")
    notifier = NotificationManager()
    
    print(f"Email configured: {'✓' if notifier.email_configured else '✗'}")
    print(f"Telegram configured: {'✓' if notifier.telegram_configured else '✗'}")
    
    return True


def main():
    """Run all tests"""
    print("=" * 60)
    print("BITCOIN MONITOR - FUNCTIONALITY TEST")
    print("=" * 60)
    
    try:
        # Test data fetching
        btc_data, fg_data, ahr999_data = test_data_fetcher()
        
        # Test data storage
        storage = test_data_storage()
        
        # Test indicators
        test_indicators(btc_data, fg_data, ahr999_data)
        
        # Test reporter
        test_reporter()
        
        # Test notifications
        test_notifications()
        
        print("\n" + "=" * 60)
        print("✓ ALL TESTS COMPLETED")
        print("=" * 60)
        print("\nIf you see mostly ✓ marks above, the system is working correctly!")
        print("You can now run: python main.py")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        print("Please check your configuration and dependencies.")


if __name__ == "__main__":
    main()
