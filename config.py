"""
Configuration settings for BitcoinMonitor
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Configuration
COINAPI_KEY = os.getenv('COINAPI_KEY', '')
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')

# Email Configuration
EMAIL_SMTP_SERVER = os.getenv('EMAIL_SMTP_SERVER', 'smtp.gmail.com')
EMAIL_SMTP_PORT = int(os.getenv('EMAIL_SMTP_PORT', '587'))
EMAIL_USERNAME = os.getenv('EMAIL_USERNAME', '')
EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD', '')
EMAIL_TO = os.getenv('EMAIL_TO', '')

# Data Storage Configuration
DATA_DIR = 'data'
HISTORICAL_DATA_FILE = os.path.join(DATA_DIR, 'btc_historical_data.csv')
REPORTS_DIR = 'reports'

# Trading Signal Thresholds
AHR999_BUY_THRESHOLD = 0.45  # Below this value suggests buying
AHR999_SELL_THRESHOLD = 1.2  # Above this value suggests selling
FEAR_GREED_BUY_THRESHOLD = 25  # Extreme fear - potential buy signal
FEAR_GREED_SELL_THRESHOLD = 75  # Extreme greed - potential sell signal

# API URLs
COINAPI_BTC_URL = 'https://rest.coinapi.io/v1/exchangerate/BTC/USD'
FEAR_GREED_API_URL = 'https://api.alternative.me/fng/'
AHR999_DATA_URL = 'https://www.coinglass.com/pro/i/ahr999'  # May need web scraping
COINGECKO_PRICE_URL = 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd'
FEAR_GREED_URL = 'https://api.alternative.me/fng/'
AHR999_URL = 'https://www.coinglass.com/pro/i/ahr999'  # May need web scraping

# Update frequency (in hours)
UPDATE_FREQUENCY = 24  # Daily updates
