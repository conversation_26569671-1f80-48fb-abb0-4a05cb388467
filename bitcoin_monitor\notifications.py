"""
Notification module for sending alerts via email and Telegram
"""
import smtplib
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON>art
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Optional Telegram import (graceful fallback if not available)
try:
    from telegram import Bo<PERSON>
    from telegram.error import TelegramError
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    logger.warning("python-telegram-bot not available. Telegram notifications disabled.")


class NotificationManager:
    """Manages email and Telegram notifications for trading alerts"""
    
    def __init__(self):
        self.email_configured = self._check_email_config()
        self.telegram_configured = self._check_telegram_config()
        
        if self.telegram_configured and TELEGRAM_AVAILABLE:
            self.telegram_bot = Bot(token=config.TELEGRAM_BOT_TOKEN)
        else:
            self.telegram_bot = None
    
    def _check_email_config(self) -> bool:
        """Check if email configuration is complete"""
        required_fields = [
            config.EMAIL_USERNAME,
            config.EMAIL_PASSWORD,
            config.EMAIL_TO
        ]
        return all(field for field in required_fields)
    
    def _check_telegram_config(self) -> bool:
        """Check if Telegram configuration is complete"""
        return bool(config.TELEGRAM_BOT_TOKEN and config.TELEGRAM_CHAT_ID)
    
    def send_email_notification(self, subject: str, body: str, is_html: bool = False) -> bool:
        """
        Send email notification
        
        Args:
            subject: Email subject
            body: Email body content
            is_html: Whether body is HTML formatted
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.email_configured:
            logger.warning("Email not configured. Skipping email notification.")
            return False
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = config.EMAIL_USERNAME
            msg['To'] = config.EMAIL_TO
            
            # Add body
            if is_html:
                msg.attach(MIMEText(body, 'html'))
            else:
                msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(config.EMAIL_SMTP_SERVER, config.EMAIL_SMTP_PORT) as server:
                server.starttls()
                server.login(config.EMAIL_USERNAME, config.EMAIL_PASSWORD)
                server.send_message(msg)
            
            logger.info(f"Email notification sent successfully to {config.EMAIL_TO}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
            return False
    
    async def send_telegram_notification(self, message: str) -> bool:
        """
        Send Telegram notification
        
        Args:
            message: Message to send
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.telegram_configured or not TELEGRAM_AVAILABLE:
            logger.warning("Telegram not configured or library not available. Skipping Telegram notification.")
            return False
        
        try:
            await self.telegram_bot.send_message(
                chat_id=config.TELEGRAM_CHAT_ID,
                text=message,
                parse_mode='Markdown'
            )
            
            logger.info("Telegram notification sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error sending Telegram notification: {e}")
            return False
    
    def send_telegram_notification_sync(self, message: str) -> bool:
        """
        Send Telegram notification (synchronous wrapper)
        
        Args:
            message: Message to send
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.telegram_configured or not TELEGRAM_AVAILABLE:
            return False
        
        try:
            # Run async function in event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.send_telegram_notification(message))
            loop.close()
            return result
            
        except Exception as e:
            logger.error(f"Error in sync Telegram notification: {e}")
            return False
    
    def format_analysis_for_email(self, analysis: Dict[str, Any]) -> tuple[str, str]:
        """
        Format analysis results for email notification
        
        Args:
            analysis: Complete analysis results
            
        Returns:
            Tuple of (subject, body) for email
        """
        try:
            # Extract key information
            market_data = analysis.get('market_data', {})
            recommendation = analysis.get('recommendation', {})
            risk_assessment = analysis.get('risk_assessment', {})
            signals = analysis.get('signals', {})
            
            # Create subject
            main_rec = recommendation.get('recommendation', 'HOLD')
            btc_price = market_data.get('btc_price', {}).get('price', 0)
            subject = f"Bitcoin Monitor Alert: {main_rec} - BTC ${btc_price:,.2f}"
            
            # Create body
            body_lines = [
                "Bitcoin Monitor Daily Analysis",
                "=" * 40,
                "",
                f"Timestamp: {analysis.get('timestamp', 'Unknown')}",
                "",
                "MARKET DATA:",
                f"• BTC Price: ${btc_price:,.2f}" if btc_price else "• BTC Price: N/A",
            ]
            
            if market_data.get('fear_greed'):
                fg_value = market_data['fear_greed']['value']
                fg_class = market_data['fear_greed']['value_classification']
                body_lines.append(f"• Fear & Greed: {fg_value}/100 ({fg_class})")
            
            if market_data.get('ahr999'):
                ahr999_value = market_data['ahr999']['value']
                body_lines.append(f"• AHR999 Index: {ahr999_value:.4f}")
            
            body_lines.extend([
                "",
                "RECOMMENDATION:",
                f"• Primary: {main_rec}",
                f"• Confidence: {recommendation.get('confidence', 'LOW')}",
                f"• Risk Level: {risk_assessment.get('risk_level', 'UNKNOWN')}",
                ""
            ])
            
            # Add alerts if any
            alerts = signals.get('alerts', [])
            if alerts:
                body_lines.append("ALERTS:")
                for alert in alerts:
                    body_lines.append(f"• {alert}")
                body_lines.append("")
            
            # Add specific actions
            specific_actions = signals.get('specific_actions', [])
            if specific_actions:
                body_lines.append("SUGGESTED ACTIONS:")
                for action in specific_actions:
                    body_lines.append(f"• {action}")
                body_lines.append("")
            
            body_lines.extend([
                "=" * 40,
                "This is an automated analysis for educational purposes only.",
                "Always do your own research and consider your risk tolerance."
            ])
            
            return subject, "\n".join(body_lines)
            
        except Exception as e:
            logger.error(f"Error formatting analysis for email: {e}")
            return "Bitcoin Monitor Error", f"Error formatting analysis: {e}"
    
    def format_analysis_for_telegram(self, analysis: Dict[str, Any]) -> str:
        """
        Format analysis results for Telegram notification
        
        Args:
            analysis: Complete analysis results
            
        Returns:
            Formatted message string for Telegram
        """
        try:
            # Extract key information
            market_data = analysis.get('market_data', {})
            recommendation = analysis.get('recommendation', {})
            risk_assessment = analysis.get('risk_assessment', {})
            signals = analysis.get('signals', {})
            
            # Get values
            main_rec = recommendation.get('recommendation', 'HOLD')
            btc_price = market_data.get('btc_price', {}).get('price', 0)
            confidence = recommendation.get('confidence', 'LOW')
            risk_level = risk_assessment.get('risk_level', 'UNKNOWN')
            
            # Create emoji indicators
            rec_emoji = {
                'STRONG_BUY': '🟢🟢',
                'BUY': '🟢',
                'HOLD': '🟡',
                'SELL': '🔴',
                'STRONG_SELL': '🔴🔴'
            }.get(main_rec, '⚪')
            
            risk_emoji = {
                'LOW': '🟢',
                'MEDIUM': '🟡',
                'HIGH': '🔴'
            }.get(risk_level, '⚪')
            
            # Build message
            message_lines = [
                "🚀 *Bitcoin Monitor Alert*",
                "",
                f"💰 *BTC Price:* ${btc_price:,.2f}" if btc_price else "💰 *BTC Price:* N/A",
            ]
            
            if market_data.get('fear_greed'):
                fg_value = market_data['fear_greed']['value']
                message_lines.append(f"😨 *Fear & Greed:* {fg_value}/100")
            
            if market_data.get('ahr999'):
                ahr999_value = market_data['ahr999']['value']
                message_lines.append(f"📈 *AHR999:* {ahr999_value:.4f}")
            
            message_lines.extend([
                "",
                f"{rec_emoji} *Recommendation:* {main_rec}",
                f"🎯 *Confidence:* {confidence}",
                f"{risk_emoji} *Risk Level:* {risk_level}",
                ""
            ])
            
            # Add alerts
            alerts = signals.get('alerts', [])
            if alerts:
                message_lines.append("🚨 *Alerts:*")
                for alert in alerts[:3]:  # Limit to 3 alerts for Telegram
                    message_lines.append(f"• {alert}")
                message_lines.append("")
            
            message_lines.append("_Automated analysis for educational purposes only._")
            
            return "\n".join(message_lines)
            
        except Exception as e:
            logger.error(f"Error formatting analysis for Telegram: {e}")
            return f"🚨 Bitcoin Monitor Error: {e}"
    
    def send_analysis_notifications(self, analysis: Dict[str, Any]) -> Dict[str, bool]:
        """
        Send notifications via all configured channels
        
        Args:
            analysis: Complete analysis results
            
        Returns:
            Dict with success status for each notification type
        """
        results = {
            'email': False,
            'telegram': False
        }
        
        try:
            # Send email notification
            if self.email_configured:
                subject, body = self.format_analysis_for_email(analysis)
                results['email'] = self.send_email_notification(subject, body)
            
            # Send Telegram notification
            if self.telegram_configured and TELEGRAM_AVAILABLE:
                message = self.format_analysis_for_telegram(analysis)
                results['telegram'] = self.send_telegram_notification_sync(message)
            
            return results
            
        except Exception as e:
            logger.error(f"Error sending analysis notifications: {e}")
            return results
    
    def send_alert_notification(self, alert_message: str, priority: str = 'NORMAL') -> Dict[str, bool]:
        """
        Send immediate alert notification
        
        Args:
            alert_message: Alert message to send
            priority: Priority level (LOW, NORMAL, HIGH)
            
        Returns:
            Dict with success status for each notification type
        """
        results = {
            'email': False,
            'telegram': False
        }
        
        try:
            # Prepare subject/message based on priority
            priority_emoji = {
                'LOW': '🔵',
                'NORMAL': '🟡',
                'HIGH': '🔴'
            }.get(priority, '⚪')
            
            subject = f"Bitcoin Monitor Alert [{priority}]: {alert_message}"
            telegram_message = f"{priority_emoji} *Bitcoin Alert*\n\n{alert_message}"
            
            # Send notifications
            if self.email_configured:
                results['email'] = self.send_email_notification(subject, alert_message)
            
            if self.telegram_configured and TELEGRAM_AVAILABLE:
                results['telegram'] = self.send_telegram_notification_sync(telegram_message)
            
            return results
            
        except Exception as e:
            logger.error(f"Error sending alert notification: {e}")
            return results
