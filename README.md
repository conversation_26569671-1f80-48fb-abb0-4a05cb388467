# BitcoinMonitor 🚀

A comprehensive Python project for monitoring Bitcoin market data and providing automated buy/sell investment recommendations based on multiple technical indicators.

## 📋 Features

- **Real-time Data Fetching**: Retrieves current BTC price, AHR999 index, and Fear & Greed index
- **Historical Data Storage**: Stores daily data in CSV/Excel format for trend analysis
- **Multi-Indicator Analysis**: Combines AHR999, Fear & Greed, and price trend analysis
- **Automated Recommendations**: Generates buy/sell signals with confidence levels
- **Risk Assessment**: Evaluates market risk and provides position sizing guidance
- **Notifications**: Email and Telegram alerts for important signals
- **Comprehensive Reporting**: Detailed console output and file reports
- **Scheduled Execution**: Automated daily analysis with scheduling

## 🏗️ Project Structure

```
bitcoin_monitor/
├── bitcoin_monitor/
│   ├── __init__.py
│   ├── data_fetcher.py      # Data acquisition from APIs
│   ├── data_storage.py      # Historical data persistence
│   ├── indicators.py        # Technical indicator calculations
│   ├── signal_generator.py  # Trading signal generation
│   ├── reporter.py          # Report generation
│   └── notifications.py     # Email/Telegram notifications
├── data/                    # Historical data storage
├── reports/                 # Generated reports
├── main.py                  # Main application entry point
├── config.py               # Configuration settings
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
└── README.md              # This file
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project
cd bitcoin_monitor

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy the environment template and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` file with your API keys and notification settings:

```env
# Optional: CoinAPI key for premium data (free tier available)
COINAPI_KEY=your_coinapi_key_here

# Optional: Telegram notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Optional: Email notifications
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_TO=<EMAIL>
```

### 3. Basic Usage

Run a single analysis:
```bash
python main.py
```

Run with scheduled daily updates:
```bash
python main.py --mode schedule
```

## 📊 Usage Examples

### Single Analysis
```bash
# Run once with notifications
python main.py --mode once

# Run once without notifications
python main.py --mode once --no-notifications

# Run once without saving reports
python main.py --mode once --no-save
```

### Scheduled Analysis
```bash
# Daily analysis (default)
python main.py --mode schedule

# Every 6 hours
python main.py --mode schedule --interval 6

# Every 12 hours with verbose logging
python main.py --mode schedule --interval 12 --verbose
```

### Utility Commands
```bash
# View data summary
python main.py --mode summary

# Test notification configuration
python main.py --mode test-notifications
```

## 🔧 Configuration Options

### API Configuration
- **CoinAPI**: Premium Bitcoin price data (optional, falls back to CoinGecko)
- **Alternative.me**: Fear & Greed Index (free)
- **AHR999**: Calculated based on price data

### Trading Signal Thresholds
Customize in `config.py`:
```python
AHR999_BUY_THRESHOLD = 0.45      # Strong buy signal
AHR999_SELL_THRESHOLD = 1.2      # Sell signal
FEAR_GREED_BUY_THRESHOLD = 25    # Extreme fear
FEAR_GREED_SELL_THRESHOLD = 75   # Extreme greed
```

### Notification Setup

#### Email Setup
1. Use Gmail with App Password (recommended)
2. Enable 2FA on your Google account
3. Generate App Password: Google Account → Security → App passwords
4. Use the app password in `EMAIL_PASSWORD`

#### Telegram Setup
1. Create a bot: Message @BotFather on Telegram
2. Get your chat ID: Message @userinfobot
3. Add bot token and chat ID to `.env`

## 📈 Understanding the Analysis

### Indicators Explained

**AHR999 Index**
- < 0.45: Excellent buying opportunity
- 0.45-1.2: Good buying range
- 1.2-5.0: Hold/neutral zone
- > 5.0: Overvalued, consider selling

**Fear & Greed Index**
- 0-25: Extreme fear (potential buy)
- 25-45: Fear (cautious buy)
- 45-55: Neutral
- 55-75: Greed (caution)
- 75-100: Extreme greed (potential sell)

### Signal Types
- **STRONG_BUY**: High confidence buying opportunity
- **BUY**: Good buying opportunity
- **HOLD**: Maintain current position
- **SELL**: Consider taking profits
- **STRONG_SELL**: High risk, consider significant profit-taking

### Risk Levels
- **LOW**: Normal market conditions
- **MEDIUM**: Increased caution recommended
- **HIGH**: Defensive positioning advised

## 📁 Data Storage

### Historical Data
- Stored in `data/btc_historical_data.csv` and `.xlsx`
- Daily records with all indicators
- Automatic deduplication for same-day updates

### Reports
- Detailed analysis reports in `reports/` directory
- JSON format for programmatic access
- Text format for human reading

## 🔍 Example Output

```
================================================================================
🚀 BITCOIN MONITOR - DAILY ANALYSIS REPORT
================================================================================
📅 Generated: 2024-01-15T10:30:00

📊 CURRENT MARKET DATA
----------------------------------------
💰 BTC Price: $42,350.75 (Source: coingecko)
😨 Fear & Greed Index: 35/100 (Fear)
📈 AHR999 Index: 0.8234

🔍 INDICATOR ANALYSIS
----------------------------------------
📊 AHR999 Signal: BUY
   └─ Good buying opportunity - price below fair value
😰 Fear & Greed Signal: WEAK_BUY
   └─ Fear in market - cautious buying opportunity

🎯 TRADING RECOMMENDATION
----------------------------------------
🟢 Primary Recommendation: BUY
🎯 Confidence Level: MEDIUM
📊 Composite Score: 0.75

⚠️  RISK ASSESSMENT
----------------------------------------
🟢 Risk Level: LOW (Score: 15/100)

🎬 ACTIONABLE SIGNALS
----------------------------------------
🎯 Primary Action: BUY
📏 Position Sizing: NORMAL_TO_LARGE
   Specific Actions:
   • Continue regular DCA strategy
   • Consider small position increase
   • Monitor for better entry points
```

## ⚠️ Important Disclaimers

- **Educational Purpose Only**: This tool is for educational and research purposes
- **Not Financial Advice**: Always do your own research and consult financial advisors
- **Risk Management**: Never invest more than you can afford to lose
- **Market Volatility**: Cryptocurrency markets are highly volatile and unpredictable
- **No Guarantees**: Past performance does not guarantee future results

## 🛠️ Development

### Adding New Indicators
1. Extend `IndicatorAnalyzer` class in `indicators.py`
2. Update `SignalGenerator` to include new indicator
3. Modify reporting templates as needed

### Customizing Signals
- Adjust thresholds in `config.py`
- Modify signal generation logic in `signal_generator.py`
- Update composite scoring in `indicators.py`

## 📝 License

This project is provided as-is for educational purposes. Use at your own risk.

## 🤝 Contributing

Feel free to submit issues, feature requests, or improvements to enhance the project's functionality.

---

**Remember**: Always practice responsible investing and never rely solely on automated analysis for financial decisions.
