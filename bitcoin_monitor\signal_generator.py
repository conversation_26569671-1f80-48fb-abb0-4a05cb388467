"""
Trading signal generation module with advanced buy/sell logic
"""
import pandas as pd
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta
from .data_fetcher import DataFetcher
from .data_storage import DataStorage
from .indicators import IndicatorAnalyzer
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SignalGenerator:
    """Generates comprehensive trading signals based on multiple indicators"""
    
    def __init__(self):
        self.data_fetcher = DataFetcher()
        self.data_storage = DataStorage()
        self.indicator_analyzer = IndicatorAnalyzer()
    
    def generate_daily_analysis(self) -> Dict[str, Any]:
        """
        Generate comprehensive daily analysis with trading signals
        
        Returns:
            Dict containing complete analysis and recommendations
        """
        try:
            logger.info("Starting daily analysis generation...")
            
            # Fetch current market data
            market_data = self.data_fetcher.fetch_all_data()
            
            # Save current data
            self.data_storage.save_daily_data(market_data)
            
            # Load historical data for trend analysis
            historical_data = self.data_storage.load_historical_data(days=30)
            
            # Initialize analysis results
            analysis = {
                'timestamp': datetime.now().isoformat(),
                'market_data': market_data,
                'analysis_results': {},
                'signals': {},
                'recommendation': {},
                'risk_assessment': {}
            }
            
            # Analyze individual indicators
            if market_data['ahr999']:
                ahr999_analysis = self.indicator_analyzer.analyze_ahr999(
                    market_data['ahr999']['value']
                )
                analysis['analysis_results']['ahr999'] = ahr999_analysis
            
            if market_data['fear_greed']:
                fear_greed_analysis = self.indicator_analyzer.analyze_fear_greed(
                    market_data['fear_greed']['value'],
                    market_data['fear_greed']['value_classification']
                )
                analysis['analysis_results']['fear_greed'] = fear_greed_analysis
            
            # Analyze price trend
            if historical_data is not None:
                trend_analysis = self.indicator_analyzer.analyze_price_trend(historical_data)
                analysis['analysis_results']['trend'] = trend_analysis
                
                # Calculate moving averages
                moving_averages = self.indicator_analyzer.calculate_moving_averages(historical_data)
                analysis['analysis_results']['moving_averages'] = moving_averages
            else:
                trend_analysis = {'trend': 'INSUFFICIENT_DATA'}
                analysis['analysis_results']['trend'] = trend_analysis
            
            # Generate composite signal
            if 'ahr999' in analysis['analysis_results'] and 'fear_greed' in analysis['analysis_results']:
                composite_signal = self.indicator_analyzer.generate_composite_signal(
                    analysis['analysis_results']['ahr999'],
                    analysis['analysis_results']['fear_greed'],
                    analysis['analysis_results']['trend']
                )
                analysis['recommendation'] = composite_signal
            
            # Generate risk assessment
            risk_assessment = self._assess_risk(analysis)
            analysis['risk_assessment'] = risk_assessment
            
            # Generate actionable signals
            signals = self._generate_actionable_signals(analysis)
            analysis['signals'] = signals
            
            # Save analysis report
            self.data_storage.save_analysis_report(analysis)
            
            logger.info("Daily analysis completed successfully")
            return analysis
            
        except Exception as e:
            logger.error(f"Error generating daily analysis: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def _assess_risk(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess current market risk based on analysis results
        
        Args:
            analysis: Complete analysis results
            
        Returns:
            Dict containing risk assessment
        """
        try:
            risk_factors = []
            risk_score = 0  # 0-100 scale
            
            # Check AHR999 risk
            if 'ahr999' in analysis['analysis_results']:
                ahr999_value = analysis['analysis_results']['ahr999'].get('value', 0)
                if ahr999_value > 5.0:
                    risk_factors.append("AHR999 indicates extreme overvaluation")
                    risk_score += 30
                elif ahr999_value > 1.2:
                    risk_factors.append("AHR999 indicates potential overvaluation")
                    risk_score += 15
            
            # Check Fear & Greed risk
            if 'fear_greed' in analysis['analysis_results']:
                fg_value = analysis['analysis_results']['fear_greed'].get('value', 50)
                if fg_value > 80:
                    risk_factors.append("Extreme greed in market sentiment")
                    risk_score += 25
                elif fg_value > 70:
                    risk_factors.append("High greed in market sentiment")
                    risk_score += 15
                elif fg_value < 20:
                    risk_factors.append("Extreme fear may indicate capitulation")
                    risk_score += 10
            
            # Check trend risk
            if 'trend' in analysis['analysis_results']:
                trend_data = analysis['analysis_results']['trend']
                if trend_data.get('trend') == 'DOWNTREND' and trend_data.get('strength') == 'STRONG':
                    risk_factors.append("Strong downtrend in progress")
                    risk_score += 20
            
            # Determine risk level
            if risk_score >= 60:
                risk_level = 'HIGH'
            elif risk_score >= 30:
                risk_level = 'MEDIUM'
            else:
                risk_level = 'LOW'
            
            return {
                'risk_level': risk_level,
                'risk_score': risk_score,
                'risk_factors': risk_factors,
                'recommendation': self._get_risk_recommendation(risk_level)
            }
            
        except Exception as e:
            logger.error(f"Error assessing risk: {e}")
            return {'risk_level': 'UNKNOWN', 'error': str(e)}
    
    def _get_risk_recommendation(self, risk_level: str) -> str:
        """Get risk-based recommendation"""
        recommendations = {
            'LOW': 'Normal position sizing and regular DCA strategy appropriate',
            'MEDIUM': 'Consider reduced position sizing and increased caution',
            'HIGH': 'Consider defensive positioning and profit-taking if in profit'
        }
        return recommendations.get(risk_level, 'Monitor market conditions closely')
    
    def _generate_actionable_signals(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate specific actionable trading signals
        
        Args:
            analysis: Complete analysis results
            
        Returns:
            Dict containing actionable signals
        """
        try:
            signals = {
                'primary_action': 'HOLD',
                'confidence': 'LOW',
                'position_sizing': 'NORMAL',
                'time_horizon': 'MEDIUM_TERM',
                'specific_actions': [],
                'alerts': []
            }
            
            # Get recommendation
            recommendation = analysis.get('recommendation', {})
            main_signal = recommendation.get('recommendation', 'HOLD')
            confidence = recommendation.get('confidence', 'LOW')
            
            signals['primary_action'] = main_signal
            signals['confidence'] = confidence
            
            # Generate specific actions based on signal
            if main_signal == 'STRONG_BUY':
                signals['specific_actions'] = [
                    'Consider increasing DCA amount by 50-100%',
                    'Good opportunity for lump sum investment',
                    'Set alerts for further price drops'
                ]
                signals['position_sizing'] = 'AGGRESSIVE'
                signals['time_horizon'] = 'LONG_TERM'
                
            elif main_signal == 'BUY':
                signals['specific_actions'] = [
                    'Continue regular DCA strategy',
                    'Consider small position increase',
                    'Monitor for better entry points'
                ]
                signals['position_sizing'] = 'NORMAL_TO_LARGE'
                
            elif main_signal == 'HOLD':
                signals['specific_actions'] = [
                    'Maintain current position',
                    'Continue regular DCA if applicable',
                    'Monitor market conditions'
                ]
                
            elif main_signal == 'SELL':
                signals['specific_actions'] = [
                    'Consider taking partial profits',
                    'Reduce position size by 25-50%',
                    'Set stop-loss levels'
                ]
                signals['position_sizing'] = 'REDUCED'
                
            elif main_signal == 'STRONG_SELL':
                signals['specific_actions'] = [
                    'Consider significant profit-taking',
                    'Reduce position size by 50-75%',
                    'Prepare for potential correction'
                ]
                signals['position_sizing'] = 'MINIMAL'
                signals['time_horizon'] = 'SHORT_TERM'
            
            # Add risk-based alerts
            risk_level = analysis.get('risk_assessment', {}).get('risk_level', 'UNKNOWN')
            if risk_level == 'HIGH':
                signals['alerts'].append('HIGH RISK: Consider defensive positioning')
            
            # Add specific threshold alerts
            if 'ahr999' in analysis['analysis_results']:
                ahr999_value = analysis['analysis_results']['ahr999'].get('value', 0)
                if ahr999_value < 0.45:
                    signals['alerts'].append('AHR999 ALERT: Excellent buying opportunity detected')
                elif ahr999_value > 5.0:
                    signals['alerts'].append('AHR999 ALERT: Extreme overvaluation detected')
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating actionable signals: {e}")
            return {'primary_action': 'HOLD', 'error': str(e)}
