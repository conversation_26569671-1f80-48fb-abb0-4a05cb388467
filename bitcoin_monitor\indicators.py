"""
Indicator calculation and analysis module for Bitcoin market signals
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class IndicatorAnalyzer:
    """Analyzes market indicators and generates trading signals"""
    
    def __init__(self):
        self.ahr999_buy_threshold = config.AHR999_BUY_THRESHOLD
        self.ahr999_sell_threshold = config.AHR999_SELL_THRESHOLD
        self.fear_greed_buy_threshold = config.FEAR_GREED_BUY_THRESHOLD
        self.fear_greed_sell_threshold = config.FEAR_GREED_SELL_THRESHOLD
    
    def analyze_ahr999(self, ahr999_value: float) -> Dict[str, Any]:
        """
        Analyze AHR999 index and provide interpretation
        
        Args:
            ahr999_value: Current AHR999 index value
            
        Returns:
            Dict containing analysis results
        """
        try:
            analysis = {
                'value': ahr999_value,
                'timestamp': datetime.now().isoformat()
            }
            
            if ahr999_value < 0.45:
                analysis.update({
                    'signal': 'STRONG_BUY',
                    'interpretation': 'Excellent buying opportunity - price significantly below fair value',
                    'confidence': 'HIGH',
                    'action': 'Consider aggressive accumulation'
                })
            elif ahr999_value < 1.2:
                analysis.update({
                    'signal': 'BUY',
                    'interpretation': 'Good buying opportunity - price below or near fair value',
                    'confidence': 'MEDIUM',
                    'action': 'Consider regular accumulation'
                })
            elif ahr999_value < 5.0:
                analysis.update({
                    'signal': 'HOLD',
                    'interpretation': 'Price near or above fair value - hold current position',
                    'confidence': 'MEDIUM',
                    'action': 'Maintain current position'
                })
            else:
                analysis.update({
                    'signal': 'SELL',
                    'interpretation': 'Price significantly overvalued - consider taking profits',
                    'confidence': 'HIGH',
                    'action': 'Consider profit-taking or position reduction'
                })
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing AHR999: {e}")
            return {'error': str(e)}
    
    def analyze_fear_greed(self, fear_greed_value: int, classification: str) -> Dict[str, Any]:
        """
        Analyze Fear & Greed index and provide interpretation
        
        Args:
            fear_greed_value: Fear & Greed index value (0-100)
            classification: Text classification from API
            
        Returns:
            Dict containing analysis results
        """
        try:
            analysis = {
                'value': fear_greed_value,
                'classification': classification,
                'timestamp': datetime.now().isoformat()
            }
            
            if fear_greed_value <= 25:
                analysis.update({
                    'signal': 'BUY',
                    'interpretation': 'Extreme fear in market - potential buying opportunity',
                    'confidence': 'HIGH',
                    'action': 'Market fear often presents good entry points'
                })
            elif fear_greed_value <= 45:
                analysis.update({
                    'signal': 'WEAK_BUY',
                    'interpretation': 'Fear in market - cautious buying opportunity',
                    'confidence': 'MEDIUM',
                    'action': 'Consider small position increases'
                })
            elif fear_greed_value <= 55:
                analysis.update({
                    'signal': 'NEUTRAL',
                    'interpretation': 'Neutral market sentiment',
                    'confidence': 'LOW',
                    'action': 'Monitor for clearer signals'
                })
            elif fear_greed_value <= 75:
                analysis.update({
                    'signal': 'WEAK_SELL',
                    'interpretation': 'Greed in market - consider caution',
                    'confidence': 'MEDIUM',
                    'action': 'Consider reducing risk exposure'
                })
            else:
                analysis.update({
                    'signal': 'SELL',
                    'interpretation': 'Extreme greed in market - high risk of correction',
                    'confidence': 'HIGH',
                    'action': 'Consider profit-taking or position reduction'
                })
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing Fear & Greed: {e}")
            return {'error': str(e)}
    
    def calculate_moving_averages(self, df: pd.DataFrame, price_column: str = 'btc_price') -> Dict[str, float]:
        """
        Calculate various moving averages from historical data
        
        Args:
            df: DataFrame with historical price data
            price_column: Column name containing price data
            
        Returns:
            Dict containing moving averages
        """
        try:
            if df is None or df.empty:
                return {}
            
            # Ensure we have enough data
            df = df.dropna(subset=[price_column])
            
            moving_averages = {}
            
            # Calculate different period moving averages
            periods = [7, 30, 50, 200]
            for period in periods:
                if len(df) >= period:
                    ma_value = df[price_column].head(period).mean()
                    moving_averages[f'ma_{period}'] = round(ma_value, 2)
            
            return moving_averages
            
        except Exception as e:
            logger.error(f"Error calculating moving averages: {e}")
            return {}
    
    def analyze_price_trend(self, df: pd.DataFrame, price_column: str = 'btc_price') -> Dict[str, Any]:
        """
        Analyze price trend from historical data
        
        Args:
            df: DataFrame with historical price data
            price_column: Column name containing price data
            
        Returns:
            Dict containing trend analysis
        """
        try:
            if df is None or df.empty or len(df) < 2:
                return {'trend': 'INSUFFICIENT_DATA'}
            
            # Get recent prices (last 7 days if available)
            recent_data = df.head(min(7, len(df)))
            prices = recent_data[price_column].dropna()
            
            if len(prices) < 2:
                return {'trend': 'INSUFFICIENT_DATA'}
            
            # Calculate trend
            first_price = prices.iloc[-1]  # Oldest in recent data
            last_price = prices.iloc[0]    # Most recent
            change_percent = ((last_price - first_price) / first_price) * 100
            
            # Determine trend strength
            if abs(change_percent) < 2:
                trend = 'SIDEWAYS'
                strength = 'WEAK'
            elif abs(change_percent) < 5:
                trend = 'UPTREND' if change_percent > 0 else 'DOWNTREND'
                strength = 'WEAK'
            elif abs(change_percent) < 10:
                trend = 'UPTREND' if change_percent > 0 else 'DOWNTREND'
                strength = 'MODERATE'
            else:
                trend = 'UPTREND' if change_percent > 0 else 'DOWNTREND'
                strength = 'STRONG'
            
            return {
                'trend': trend,
                'strength': strength,
                'change_percent': round(change_percent, 2),
                'period_days': len(prices),
                'current_price': last_price,
                'period_start_price': first_price
            }
            
        except Exception as e:
            logger.error(f"Error analyzing price trend: {e}")
            return {'trend': 'ERROR', 'error': str(e)}
    
    def generate_composite_signal(self, ahr999_analysis: Dict, fear_greed_analysis: Dict, 
                                 trend_analysis: Dict) -> Dict[str, Any]:
        """
        Generate composite trading signal based on multiple indicators
        
        Args:
            ahr999_analysis: AHR999 analysis results
            fear_greed_analysis: Fear & Greed analysis results
            trend_analysis: Price trend analysis results
            
        Returns:
            Dict containing composite signal and recommendation
        """
        try:
            # Extract signals
            ahr999_signal = ahr999_analysis.get('signal', 'NEUTRAL')
            fear_greed_signal = fear_greed_analysis.get('signal', 'NEUTRAL')
            trend = trend_analysis.get('trend', 'SIDEWAYS')
            
            # Signal scoring system
            signal_scores = {
                'STRONG_BUY': 2, 'BUY': 1, 'WEAK_BUY': 0.5,
                'NEUTRAL': 0, 'HOLD': 0,
                'WEAK_SELL': -0.5, 'SELL': -1, 'STRONG_SELL': -2
            }
            
            # Calculate composite score
            ahr999_score = signal_scores.get(ahr999_signal, 0)
            fear_greed_score = signal_scores.get(fear_greed_signal, 0)
            
            # Weight the signals (AHR999 gets higher weight as it's more fundamental)
            composite_score = (ahr999_score * 0.6) + (fear_greed_score * 0.4)
            
            # Adjust for trend
            if trend == 'UPTREND':
                composite_score += 0.2
            elif trend == 'DOWNTREND':
                composite_score -= 0.2
            
            # Generate final recommendation
            if composite_score >= 1.5:
                recommendation = 'STRONG_BUY'
                confidence = 'HIGH'
            elif composite_score >= 0.5:
                recommendation = 'BUY'
                confidence = 'MEDIUM'
            elif composite_score >= -0.5:
                recommendation = 'HOLD'
                confidence = 'LOW'
            elif composite_score >= -1.5:
                recommendation = 'SELL'
                confidence = 'MEDIUM'
            else:
                recommendation = 'STRONG_SELL'
                confidence = 'HIGH'
            
            return {
                'recommendation': recommendation,
                'confidence': confidence,
                'composite_score': round(composite_score, 2),
                'contributing_signals': {
                    'ahr999': ahr999_signal,
                    'fear_greed': fear_greed_signal,
                    'trend': trend
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating composite signal: {e}")
            return {'recommendation': 'ERROR', 'error': str(e)}
    
    def calculate_ahr999_index(self, df: pd.DataFrame) -> Optional[float]:
        """
        Calculate the AHR999 index using historical data.
        
        AHR999 = (BTC Price / 200-day MA) / (2-year MA / 200-day MA)
        
        Args:
            df: DataFrame with historical price data containing 'btc_price' column
                Data should be sorted with most recent dates first
            
        Returns:
            float: AHR999 index value or None if insufficient data
        """
        try:
            if df is None or df.empty:
                return None
            
            # Check if we have enough data (at least 2 years of daily data)
            if len(df) < 730:  # 2 years * 365 days
                logger.warning("Insufficient data for AHR999 calculation (need at least 2 years)")
                return None
            
            # Clean data
            df = df.dropna(subset=['btc_price'])
            if len(df) < 730:
                logger.warning("Insufficient clean data for AHR999 calculation")
                return None
            
            # Get current price (most recent)
            current_price = df['btc_price'].iloc[0]
            
            # Calculate 200-day moving average
            ma_200 = df['btc_price'].head(200).mean()
            
            # Calculate 2-year (730-day) moving average
            ma_2year = df['btc_price'].head(730).mean()
            
            # Calculate AHR999 index
            if ma_200 == 0 or ma_2year == 0:
                return None
                
            ahr999 = (current_price / ma_200) / (ma_2year / ma_200)
            
            return round(ahr999, 4)
            
        except Exception as e:
            logger.error(f"Error calculating AHR999 index: {e}")
            return None
    
    def calculate_price_relative_to_ma(self, df: pd.DataFrame) -> Dict[str, Optional[float]]:
        """
        Calculate BTC price relative to its moving averages.
        
        Args:
            df: DataFrame with historical price data containing 'btc_price' column
                Data should be sorted with most recent dates first
            
        Returns:
            Dict containing price ratios to moving averages
        """
        try:
            if df is None or df.empty:
                return {'price_to_ma200': None, 'price_to_ma2year': None}
            
            # Clean data
            df = df.dropna(subset=['btc_price'])
            if df.empty:
                return {'price_to_ma200': None, 'price_to_ma2year': None}
            
            # Get current price (most recent)
            current_price = df['btc_price'].iloc[0]
            
            # Calculate 200-day moving average if enough data
            ma_200 = None
            if len(df) >= 200:
                ma_200 = df['btc_price'].head(200).mean()
            
            # Calculate 2-year (730-day) moving average if enough data
            ma_2year = None
            if len(df) >= 730:
                ma_2year = df['btc_price'].head(730).mean()
            
            # Calculate ratios
            price_to_ma200 = None
            price_to_ma2year = None
            
            if ma_200 and ma_200 != 0:
                price_to_ma200 = round(current_price / ma_200, 4)
                
            if ma_2year and ma_2year != 0:
                price_to_ma2year = round(current_price / ma_2year, 4)
            
            return {
                'price_to_ma200': price_to_ma200,
                'price_to_ma2year': price_to_ma2year
            }
            
        except Exception as e:
            logger.error(f"Error calculating price relative to moving averages: {e}")
            return {'price_to_ma200': None, 'price_to_ma2year': None}
    
    def calculate_volatility_index(self, df: pd.DataFrame, period: int = 30) -> Optional[float]:
        """
        Calculate price volatility over a specified period.
        
        Args:
            df: DataFrame with historical price data containing 'btc_price' column
                Data should be sorted with most recent dates first
            period: Number of days to calculate volatility for (default: 30)
            
        Returns:
            float: Volatility index (coefficient of variation) or None if insufficient data
        """
        try:
            if df is None or df.empty:
                return None
            
            # Check if we have enough data
            if len(df) < period:
                logger.warning(f"Insufficient data for volatility calculation (need at least {period} days)")
                return None
            
            # Clean data
            df = df.dropna(subset=['btc_price'])
            if len(df) < period:
                logger.warning("Insufficient clean data for volatility calculation")
                return None
            
            # Get price data for the specified period
            prices = df['btc_price'].head(period)
            
            # Calculate volatility (coefficient of variation)
            if prices.std() == 0:
                return 0.0
                
            volatility = (prices.std() / prices.mean()) * 100
            
            return round(volatility, 4)
            
        except Exception as e:
            logger.error(f"Error calculating volatility index: {e}")
            return None
    
    def normalize_fear_greed(self, fear_greed_value: int) -> Dict[str, Any]:
        """
        Normalize Fear & Greed index and provide interpretation.
        
        Args:
            fear_greed_value: Raw Fear & Greed index value (0-100)
            
        Returns:
            Dict containing normalized value and interpretation
        """
        try:
            if fear_greed_value is None:
                return {'normalized_value': None, 'interpretation': 'No data'}
            
            # Fear & Greed is already on a 0-100 scale, so we just interpret it
            if fear_greed_value <= 20:
                interpretation = 'Extreme Fear'
            elif fear_greed_value <= 40:
                interpretation = 'Fear'
            elif fear_greed_value <= 60:
                interpretation = 'Neutral'
            elif fear_greed_value <= 80:
                interpretation = 'Greed'
            else:
                interpretation = 'Extreme Greed'
            
            return {
                'normalized_value': fear_greed_value,
                'interpretation': interpretation
            }
            
        except Exception as e:
            logger.error(f"Error normalizing Fear & Greed index: {e}")
            return {'normalized_value': None, 'error': str(e)}
