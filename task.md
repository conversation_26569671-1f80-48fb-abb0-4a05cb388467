Python Developer Coding Test
 Project Overview
 BitcoinMonitor is an Python project designed to monitor Bitcoin (BTC) market data daily and
 provide  automated  buy/sell  investment  recommendations.  The  system  analyzes  BTC  price  trends,  the
 AHR999 index, and the Fear & Greed index based on historical data to generate insightful AI-driven trading
 advice.
 Test Objective
 Candidates will extend or modify BitconMonitor to demonstrate their skills in:- Data fetching and parsing (from APIs or web sources)- Historical data storage and management- Indicator calculation and analysis- Implementing buy/sell signal logic based on multiple indicators- Clean, modular, maintainable Python coding- Clear reporting or alert generation
 Candidate Tasks
 1. Enhance Data Acquisition
   -
 Implement or improve methods to fetch and parse the latest BTC price, AHR999 index, and Fear & Greed
 index data(optional).
 2. Data Persistence
   -
 Store daily historical data in a csv, excel or database format.
 3. Reporting
   -
   -
 Generate a summary report or console output displaying the latest analysis and recommendation.
 Implement notification or alert features (e.g., email, telegram).
 4. Code Quality & Documentation
   -
   -
 Write clean, modular, and well-documented Python code.
 Include clear README instructions for setup, configuration, and running the program.
 Basic Project
   -
 Complete a python project with specific requirements based on the basic project